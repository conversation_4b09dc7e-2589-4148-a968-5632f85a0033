## 本脚本要放到 ocrflux 源码文件夹中

import time
import os
from pathlib import Path
from vllm import LLM
from ocrflux.inference import parse

def process_pdf_to_markdown(file_path):
    """
    处理PDF文件并转换为Markdown格式，记录耗时

    Args:
        file_path: PDF文件路径
    """
    print(f"开始处理文件: {file_path}")

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return

    # 生成输出文件路径（同目录下，同名但扩展名为.md）
    input_path = Path(file_path)
    output_path = input_path.with_suffix('.md')

    print(f"输出文件将保存为: {output_path}")

    # 记录开始时间
    start_time = time.time()

    try:
        # 初始化模型
        print("正在初始化模型...")
        model_init_start = time.time()
        llm = LLM(model="/root/autodl-tmp/OCRFlux/models/OCRFlux-3B",
                 gpu_memory_utilization=0.8,
                 max_model_len=8192)
        model_init_time = time.time() - model_init_start
        print(f"模型初始化完成，耗时: {model_init_time:.2f} 秒")

        # 解析PDF
        print("正在解析PDF...")
        parse_start = time.time()
        result = parse(llm, file_path)
        parse_time = time.time() - parse_start
        print(f"PDF解析完成，耗时: {parse_time:.2f} 秒")

        if result != None:
            document_markdown = result['document_text']

            # 保存到MD文件
            print("正在保存Markdown文件...")
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(document_markdown)

            # 计算总耗时
            total_time = time.time() - start_time

            # 输出结果
            print("\n" + "="*50)
            print("处理完成!")
            print(f"输入文件: {file_path}")
            print(f"输出文件: {output_path}")
            print(f"模型初始化耗时: {model_init_time:.2f} 秒")
            print(f"PDF解析耗时: {parse_time:.2f} 秒")
            print(f"总耗时: {total_time:.2f} 秒")
            print("="*50)

            # 显示部分内容预览
            print("\n内容预览:")
            print("-" * 30)
            preview = document_markdown[:500] + "..." if len(document_markdown) > 500 else document_markdown
            print(preview)
            print("-" * 30)

        else:
            print("错误: PDF解析失败")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        total_time = time.time() - start_time
        print(f"失败前耗时: {total_time:.2f} 秒")

if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # 默认文件路径
        file_path = r'/root/autodl-tmp/data/page_02_left.pdf'
        print(f"未指定文件路径，使用默认路径: {file_path}")
        print("使用方法: python run.py <PDF文件路径>")

    # 处理文件
    process_pdf_to_markdown(file_path)