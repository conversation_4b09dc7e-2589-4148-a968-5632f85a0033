import fitz  # PyMuPDF
import os
from datetime import datetime

local_host = "http://localhost:5003"

def split_pages_by_page(input_pdf_path, output_dir=None, timestamp=None):
    """
    将PDF按页拆分，每页保存为单独的PDF文件
    返回所有拆分后PDF文件的信息数组，每个元素包含 filename 和 url 字段
    """
    # 生成带时间戳的输出目录
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if output_dir is None:
        output_dir = os.path.join("data", timestamp)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 打开原始PDF
    source_pdf = fitz.open(input_pdf_path)

    print(f"开始按页拆分PDF: {input_pdf_path}")
    print(f"总页数: {len(source_pdf)}")
    print(f"输出目录: {output_dir}")

    pdf_files = []  # 用于收集所有拆分后PDF文件的信息

    for page_num in range(len(source_pdf)):
        # 创建新的PDF文档，只包含当前页
        page_pdf = fitz.open()
        page_pdf.insert_pdf(source_pdf, from_page=page_num, to_page=page_num)

        # 生成文件名
        page_filename = f"page_{page_num+1:02d}.pdf"
        page_filepath = os.path.join(output_dir, page_filename)

        # 保存单页PDF
        page_pdf.save(page_filepath)
        page_pdf.close()

        # 收集PDF信息
        pdf_files.append({
            'filename': page_filename,
            'url': f"{local_host}/uploads/original/{timestamp}/{page_filename}",
            'filepath': f"{output_dir}/{page_filename}"
        })

        print(f"第 {page_num + 1} 页拆分完成: {page_filepath}")

    source_pdf.close()
    print(f"✅ 所有页面按页拆分完成! 文件保存在: {output_dir}")
    return pdf_files

def split_pages_individually(input_pdf_path, output_dir=None,timestamp=None):
    """
    将PDF每页拆分成左右两部分，并单独保存每个部分
    返回所有拆分后PDF文件的信息数组，每个元素包含 filename 和 url 字段
    """
    # 生成带时间戳的输出目录
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if output_dir is None:
        output_dir = os.path.join("data", timestamp)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 打开原始PDF
    source_pdf = fitz.open(input_pdf_path)

    print(f"开始拆分PDF: {input_pdf_path}")
    print(f"总页数: {len(source_pdf)}")
    print(f"输出目录: {output_dir}")

    pdf_files = []  # 新增：用于收集所有拆分后PDF文件的信息

    for page_num in range(len(source_pdf)):
        page = source_pdf[page_num]
        rect = page.rect

        # 检查页面旋转信息
        rotation = page.rotation
        print(f"第 {page_num + 1} 页信息:")
        print(f"  尺寸: {rect.width} x {rect.height}")
        print(f"  旋转角度: {rotation}°")

        # 计算分割位置（宽度的50%）
        mid_x = rect.width / 2

        # 左半部分和右半部分的矩形区域
        left_rect = fitz.Rect(0, 0, mid_x, rect.height)
        right_rect = fitz.Rect(mid_x, 0, rect.width, rect.height)

        # 创建左半部分PDF
        left_pdf = fitz.open()
        left_page = left_pdf.new_page(width=mid_x, height=rect.height)
        if rotation != 0:
            left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
        else:
            left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)
        left_filename = f"page_{page_num+1:02d}_left.pdf"
        left_filepath = os.path.join(output_dir, left_filename)
        left_pdf.save(left_filepath)
        left_pdf.close()
        # 新增：收集左半部分PDF信息
        pdf_files.append({
            'filename': left_filename,
            'url': f"{local_host}/uploads/original/{timestamp}/{left_filename}",
            'filepath': f"{output_dir}/{left_filename}"
        })

        # 创建右半部分PDF
        right_pdf = fitz.open()
        right_page = right_pdf.new_page(width=rect.width - mid_x, height=rect.height)
        if rotation != 0:
            right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
        else:
            right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)
        right_filename = f"page_{page_num+1:02d}_right.pdf"
        right_filepath = os.path.join(output_dir, right_filename)
        right_pdf.save(right_filepath)
        right_pdf.close()
        # 新增：收集右半部分PDF信息
        pdf_files.append({
            'filename': right_filename,
            'url': f"{local_host}/uploads/original/{timestamp}/{right_filename}",
            'filepath': f"{output_dir}/{right_filename}"
        })

        print(f"第 {page_num + 1} 页拆分完成:")
        print(f"  左半部分: {left_filepath}")
        print(f"  右半部分: {right_filepath}")

    source_pdf.close()
    print(f"✅ 所有页面拆分完成! 文件保存在: {output_dir}")
    return pdf_files

def split_and_merge_pdf(input_pdf_path, output_pdf_path):
    """
    将PDF按页拆分，每页按宽度50%二次切分，最后合并成新PDF
    """
    # 打开原始PDF
    source_pdf = fitz.open(input_pdf_path)
    # 创建新的PDF文档用于输出
    output_pdf = fitz.open()

    print(f"开始处理PDF: {input_pdf_path}")
    print(f"总页数: {len(source_pdf)}")

    for page_num in range(len(source_pdf)):
        page = source_pdf[page_num]
        rect = page.rect
        rotation = page.rotation

        # 计算分割位置（宽度的50%）
        mid_x = rect.width / 2

        # 左半部分和右半部分的矩形区域
        left_rect = fitz.Rect(0, 0, mid_x, rect.height)
        right_rect = fitz.Rect(mid_x, 0, rect.width, rect.height)

        # 判断是否为偶数页（第2,4,6...页）
        if (page_num + 1) % 2 == 0:  # 偶数页，先添加右半部分，再添加左半部分
            # 先添加右半部分
            right_page = output_pdf.new_page(width=rect.width - mid_x, height=rect.height)
            if rotation != 0:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
            else:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)

            # 再添加左半部分
            left_page = output_pdf.new_page(width=mid_x, height=rect.height)
            if rotation != 0:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
            else:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)

            print(f"处理第 {page_num + 1} 页完成 (偶数页，右→左顺序，旋转: {rotation}°)")
        else:  # 奇数页，先添加左半部分，再添加右半部分
            # 先添加左半部分
            left_page = output_pdf.new_page(width=mid_x, height=rect.height)
            if rotation != 0:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect, rotate=-rotation)
            else:
                left_page.show_pdf_page(left_page.rect, source_pdf, page_num, clip=left_rect)

            # 再添加右半部分
            right_page = output_pdf.new_page(width=rect.width - mid_x, height=rect.height)
            if rotation != 0:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect, rotate=-rotation)
            else:
                right_page.show_pdf_page(right_page.rect, source_pdf, page_num, clip=right_rect)

            print(f"处理第 {page_num + 1} 页完成 (奇数页，左→右顺序，旋转: {rotation}°)")

    # 保存处理后的PDF
    output_pdf.save(output_pdf_path)
    output_pdf.close()
    source_pdf.close()

    print(f"✅ 处理完成! 输出文件: {output_pdf_path}")

def split_pdf(input_file, report_mode):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join("data", timestamp)
    base_name = os.path.splitext(os.path.basename(input_file))[0]

    if report_mode == "single":
        # single模式：按页拆分，不进行左右分割，然后合并
        result_info = split_pages_by_page(input_file, output_dir, timestamp)
        # 复制原PDF到当前文件夹，重命名为new-xxx.pdf
        import shutil
        output_file = os.path.join(output_dir, f"new-{base_name}.pdf")
        shutil.copy2(input_file, output_file)
        return {
            "split_pdfs": result_info,
            "merged_pdf": {
                "url": f"{local_host}/uploads/original/{timestamp}/new-{base_name}.pdf",
                "filepath": output_file,
                "filename": f"new-{base_name}.pdf"
            },
            "timestamp": timestamp,
            "data_dir": output_dir
        }
    else:
        # 默认模式：左右分割每页，然后合并
        result_info = split_pages_individually(input_file, output_dir, timestamp)
    
        output_file = os.path.join(output_dir, f"new-{base_name}.pdf")
        split_and_merge_pdf(input_file, output_file)
        return {
            "split_pdfs": result_info,
            "merged_pdf": {
                "url": f"{local_host}/uploads/original/{timestamp}/new-{base_name}.pdf",
                "filepath": output_file,
                "filename": f"new-{base_name}.pdf"
            },
            "timestamp": timestamp,
            "data_dir": output_dir
        }

if __name__ == "__main__":
    input_file = "456.pdf"  # 替换为你的输入文件路径
    result_info = split_pdf(input_file,'double')
    # 演示如何获取结果信息
    print("===  获取结果信息对象 ===")
    print(result_info)