import fitz  # PyMuPDF

def check_pdf_info(pdf_path):
    """
    检查PDF文件的详细信息
    """
    pdf_document = fitz.open(pdf_path)
    
    print(f"PDF文件: {pdf_path}")
    print(f"总页数: {len(pdf_document)}")
    print("-" * 50)
    
    for page_num in range(len(pdf_document)):
        page = pdf_document[page_num]
        
        print(f"第 {page_num + 1} 页:")
        print(f"  页面矩形 (rect): {page.rect}")
        print(f"  媒体框 (mediabox): {page.mediabox}")
        print(f"  裁剪框 (cropbox): {page.cropbox}")
        print(f"  旋转角度: {page.rotation}°")
        
        # 获取页面的变换矩阵
        matrix = page.transformation_matrix
        print(f"  变换矩阵: {matrix}")
        
        # 检查页面是否有特殊的坐标系统
        if page.rotation != 0:
            print(f"  ⚠️  页面有旋转: {page.rotation}°")
        
        print()
    
    pdf_document.close()

if __name__ == "__main__":
    check_pdf_info("456.pdf")
