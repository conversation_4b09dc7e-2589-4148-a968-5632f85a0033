# -*- coding: utf-8 -*-
from flask import Flask, request, jsonify,send_file,send_from_directory
from flask_cors import CORS
import mimetypes
import os
from werkzeug.utils import secure_filename
from core import process_pdf_to_markdown_via_service as ocr_process
import time
from datetime import datetime
from 征信拆分 import split_pdf

# 添加正确的 MIME 类型
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('text/css', '.css')

# 创建Flask应用并配置静态文件目录
app = Flask(__name__, static_folder='static', static_url_path='')
CORS(app)  # 启用CORS支持

# 获取当前文件所在目录的绝对路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# 创建绝对路径的data目录
DATA_FOLDER = os.path.join(BASE_DIR, 'data')
if not os.path.exists(DATA_FOLDER):
    os.makedirs(DATA_FOLDER)

def get_timestamp_filename(filename):
    """生成带时间戳的文件名"""
    # 确保文件名安全
    safe_name = secure_filename(filename)

    # 分离文件名和扩展名
    name, ext = os.path.splitext(safe_name)

    # 如果文件名被清空，使用默认名
    if not name:
        name = "file"

    # 添加时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"{name}_{timestamp}{ext}"

# 处理静态文件的路由
@app.route('/<path:filename>')
def serve_static(filename):
    # 为 .js 文件设置正确的 MIME 类型
    if filename.endswith('.js'):
        return send_from_directory(app.static_folder, filename,
                                 mimetype='application/javascript')
    return send_from_directory(app.static_folder, filename)

# 处理所有前端路由
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def catch_all(path):
    return app.send_static_file('index.html')

@app.route('/api/credit/word', methods=['POST'])
def download_word():
    data = request.get_json()
    if not data or 'content' not in data:
        return jsonify({'success': False, 'error': '缺少markdown内容'}), 400
    md_content = data['content']
    # 生成唯一文件名
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    md_filename = f"{timestamp}.md"
    md_filename2 = f"{timestamp}"
    docx_filename = f"{timestamp}.docx"
    md_path = os.path.join(DATA_FOLDER, md_filename)
    md_path2 = os.path.join(DATA_FOLDER, md_filename2)
    docx_path = os.path.join(DATA_FOLDER, docx_filename)
    # 保存markdown内容
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    # 调用Markdown2docx生成word
    from Markdown2docx import Markdown2docx
    project = Markdown2docx(md_path2)
    project.eat_soup()
    project.save()
    # 返回word文件
    return send_file(docx_path, as_attachment=True, download_name=docx_filename)


## 文件已经上传过了，传入filepath进来 直接进行ocr
@app.route('/api/credit/ocr', methods=['POST'])
def credit_ocr():
    data = request.get_json()
    if not data or 'filepath' not in data:
        return jsonify({'success': False, 'error': '缺少filepath内容'}), 400
    filepath = data['filepath']
    page_range = data['page_range']
    try:
        result = ocr_process(filepath,  page_range)
        return jsonify({"success": True, "result": result, "extract_content": page_range})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/credit/process', methods=['POST'])
def credit_process():
    # 支持 form-data 文件上传
    if 'file' not in request.files:
        return jsonify({"success": False, "error": "没有文件"}), 400
    file = request.files['file']
    if file.filename == '':
        return jsonify({"success": False, "error": "没有选择文件"}), 400

    report_mode = request.form.get('report_mode', 'single')
    print('pdf报告模式:', report_mode)

    filename = secure_filename(file.filename)

    save_path = os.path.join(DATA_FOLDER, filename)
    if os.path.exists(save_path):
        os.remove(save_path)
    file.save(save_path)
    try:
        result = split_pdf(save_path,report_mode)
        return jsonify({"success": True, "result": result})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})



@app.route('/uploads/original/<path:filename>')
def serve_original_upload(filename):
    # filename 可能是 20240601_123456/page_01_left.pdf 这样的
    file_path = os.path.join(DATA_FOLDER, filename)
    if not os.path.isfile(file_path):
        return jsonify({"error": "File not found"}), 404
    return send_file(file_path)

## 征信报告ocr识别 5003端口 
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5003, debug=True)
