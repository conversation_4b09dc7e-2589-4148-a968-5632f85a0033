
按顺序执行命令:

linux依赖安装

    sudo apt-get update
    sudo apt-get install poppler-utils poppler-data ttf-mscorefonts-installer msttcorefonts fonts-crosextra-caladea fonts-crosextra-carlito gsfonts lcdf-typetools

创建venv

    conda create -n ocrflux python=3.11
    
    conda activate ocrflux

如果激活环境报错，请执行
    
    conda init "$(basename "${SHELL}")"

重新打开终端，重新激活

    conda activate ocrflux

设置 github 加速

    source /etc/network_turbo

下载代码

    git clone https://github.com/chatdoc-com/OCRFlux.git
    cd OCRFlux
    
    pip install -e . --find-links https://flashinfer.ai/whl/cu124/torch2.5/flashinfer/
    pip install huggingface_hub


下载模型

    cd /root/autodl-tmp/OCRFlux/
    mkdir models
    python -c "from huggingface_hub import snapshot_download; snapshot_download(repo_id='ChatDOC/OCRFlux-3B', local_dir='/root/autodl-tmp/OCRFlux/models/OCRFlux-3B')"

    [注意model存放路径，如果下载失败，可以重新下载，多试几次]


第一种运行

    python -m ocrflux.pipeline ./localworkspace --data /root/autodl-tmp/data/123.pdf --model /root/autodl-tmp/OCRFlux/models/OCRFlux-3B

    # 2. 设置执行权限
    chmod +x run.sh
    ./run.sh /root/autodl-tmp/data/123.pdf


第二种运行

    cd /root/autodl-tmp/OCRFlux/ocrflux/

上传run.py 到源码目录

    python run.py  /root/autodl-tmp/data/123.pdf

第三种运行

    启动vllm
    
    cd /root/autodl-tmp/OCRFlux/ocrflux
    chmod +x server.sh
    ./server.sh /root/autodl-tmp/OCRFlux/models/OCRFlux-3B 30024

    python http.py /root/autodl-tmp/data/page_03_left.pdf

    